#!/usr/bin/env python3
"""
API Endpoints Test Script for Load Image functionality

This script tests the actual FastAPI endpoints to ensure they work correctly.
Run this after starting the FastAPI server.
"""

import asyncio
import aiohttp
import base64
import json
import os
from pathlib import Path


async def test_health_endpoint():
    """Test the health check endpoint"""
    print("🏥 Testing Health Check Endpoint")
    print("=" * 40)
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8000/api/v1/image-processing/health') as response:
                if response.status == 200:
                    data = await response.json()
                    print("✅ Health check passed")
                    print(f"   Status: {data.get('data', {}).get('overall_status', 'unknown')}")
                    print(f"   Azure OpenAI Vision: {data.get('data', {}).get('azure_openai_vision', 'unknown')}")
                    print(f"   Supported types: {data.get('data', {}).get('supported_image_types', [])}")
                    print(f"   AWS services count: {data.get('data', {}).get('aws_services_count', 0)}")
                else:
                    print(f"❌ Health check failed: {response.status}")
                    
    except Exception as e:
        print(f"❌ Health check error: {e}")


async def test_aws_services_endpoint():
    """Test the AWS services catalog endpoint"""
    print("\n📋 Testing AWS Services Catalog Endpoint")
    print("=" * 40)
    
    try:
        async with aiohttp.ClientSession() as session:
            # Note: This endpoint requires authentication, so we'll test without auth first
            async with session.get('http://localhost:8000/api/v1/image-processing/aws-services') as response:
                if response.status == 401:
                    print("✅ Endpoint requires authentication (expected)")
                elif response.status == 200:
                    data = await response.json()
                    print(f"✅ Found {data.get('total_count', 0)} AWS services")
                    print(f"   Sample services: {data.get('services', [])[:5]}")
                else:
                    print(f"❌ Unexpected status: {response.status}")
                    
    except Exception as e:
        print(f"❌ AWS services endpoint error: {e}")


async def test_service_validation_endpoint():
    """Test the service validation endpoint"""
    print("\n✅ Testing Service Validation Endpoint")
    print("=" * 40)
    
    test_services = ["AWS Lambda", "Amazon S3", "Invalid Service", "User"]
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                'http://localhost:8000/api/v1/image-processing/validate-services',
                json=test_services,
                headers={'Content-Type': 'application/json'}
            ) as response:
                if response.status == 401:
                    print("✅ Endpoint requires authentication (expected)")
                elif response.status == 200:
                    data = await response.json()
                    validation_data = data.get('data', {})
                    print(f"✅ Validation completed")
                    print(f"   Valid services: {validation_data.get('valid_services', [])}")
                    print(f"   Invalid services: {validation_data.get('invalid_services', [])}")
                else:
                    print(f"❌ Unexpected status: {response.status}")
                    
    except Exception as e:
        print(f"❌ Service validation endpoint error: {e}")


async def create_test_image_file():
    """Create a test image file for upload testing"""
    try:
        from PIL import Image, ImageDraw
        
        # Create a simple test image
        img = Image.new('RGB', (400, 300), color='white')
        draw = ImageDraw.Draw(img)
        
        # Draw simple architecture elements
        # User box
        draw.rectangle([50, 50, 150, 100], outline='blue', width=2)
        draw.text((75, 70), "User", fill='blue')
        
        # Lambda box
        draw.rectangle([200, 50, 350, 100], outline='orange', width=2)
        draw.text((225, 70), "AWS Lambda", fill='orange')
        
        # Arrow
        draw.line([(150, 75), (200, 75)], fill='black', width=2)
        draw.polygon([(190, 70), (200, 75), (190, 80)], fill='black')
        
        # Save test image
        test_image_path = Path(__file__).parent / "test_api_image.png"
        img.save(test_image_path)
        print(f"✅ Created test image: {test_image_path}")
        return test_image_path
        
    except ImportError:
        print("⚠️  Pillow not installed, cannot create test image")
        return None


async def test_load_image_endpoint():
    """Test the main load image endpoint"""
    print("\n🖼️  Testing Load Image Endpoint")
    print("=" * 40)
    
    # Create test image
    test_image_path = await create_test_image_file()
    if not test_image_path or not test_image_path.exists():
        print("❌ Cannot test without image file")
        return
    
    try:
        async with aiohttp.ClientSession() as session:
            # Prepare multipart form data
            with open(test_image_path, 'rb') as f:
                data = aiohttp.FormData()
                data.add_field('file', f, filename='test_architecture.png', content_type='image/png')
                
                async with session.post(
                    'http://localhost:8000/api/v1/image-processing/load-image',
                    data=data
                ) as response:
                    if response.status == 401:
                        print("✅ Endpoint requires authentication (expected)")
                        print("   To test with authentication, provide a valid JWT token")
                    elif response.status == 200:
                        data = await response.json()
                        print("✅ Image processing completed successfully")
                        print(f"   Task ID: {data.get('task_id', 'unknown')}")
                        print(f"   Status: {data.get('status', 'unknown')}")
                        print(f"   Message: {data.get('message', 'unknown')}")
                        
                        if data.get('data'):
                            arch_data = data['data']
                            print(f"   Nodes: {len(arch_data.get('nodes', []))}")
                            print(f"   Edges: {len(arch_data.get('edges', []))}")
                            
                        if data.get('metadata'):
                            metadata = data['metadata']
                            print(f"   Services identified: {metadata.get('services_identified', 0)}")
                            print(f"   Connections found: {metadata.get('connections_found', 0)}")
                    else:
                        error_text = await response.text()
                        print(f"❌ Image processing failed: {response.status}")
                        print(f"   Error: {error_text}")
                        
    except Exception as e:
        print(f"❌ Load image endpoint error: {e}")


async def test_server_connectivity():
    """Test if the FastAPI server is running"""
    print("🔌 Testing Server Connectivity")
    print("=" * 40)
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8000/docs') as response:
                if response.status == 200:
                    print("✅ FastAPI server is running")
                    print("   Swagger docs available at: http://localhost:8000/docs")
                else:
                    print(f"⚠️  Server responded with status: {response.status}")
                    
    except aiohttp.ClientConnectorError:
        print("❌ Cannot connect to FastAPI server")
        print("   Make sure the server is running on http://localhost:8000")
        print("   Start with: uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
        return False
    except Exception as e:
        print(f"❌ Server connectivity error: {e}")
        return False
    
    return True


async def main():
    """Main test function"""
    print("🚀 Load Image API Endpoints Test Suite")
    print("=" * 50)
    
    # Test server connectivity first
    server_running = await test_server_connectivity()
    
    if not server_running:
        print("\n❌ Cannot proceed without server running")
        return
    
    # Test all endpoints
    await test_health_endpoint()
    await test_aws_services_endpoint()
    await test_service_validation_endpoint()
    await test_load_image_endpoint()
    
    print("\n🎉 API endpoint tests completed!")
    print("\nNotes:")
    print("- Most endpoints require JWT authentication")
    print("- Health check endpoint is public")
    print("- To test authenticated endpoints, obtain a JWT token first")
    print("- Image processing requires Azure OpenAI credentials")


if __name__ == "__main__":
    asyncio.run(main())
