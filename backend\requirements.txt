# FastAPI and web framework dependencies
fastapi==0.104.1
uvicorn[standard]==0.23.2
python-multipart==0.0.9

# Machine Learning and Data Science
numpy==1.26.1
pandas==2.2.2
scikit-learn==1.5.1
joblib==1.4.2
xgboost==2.0.3

# RAG and NLP dependencies
langchain==0.1.12
langchain-community==0.0.32
langchain-core==0.1.42
langchain-text-splitters==0.0.1
sentence-transformers==2.2.2
huggingface-hub==0.19.4
transformers==4.39.3
chromadb==0.4.24

# Azure OpenAI (updated for GPT-4o vision support)
openai==1.12.0

# Document processing
PyPDF2==3.0.1
python-docx==1.1.0
docx2txt==0.8

# Vector databases
pinecone-client==2.2.4

# Database dependencies
sqlalchemy==2.0.23
alembic==1.13.1

# Authentication dependencies
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
bcrypt==4.0.1
python-multipart==0.0.9

# Environment and configuration
python-dotenv==1.0.1
pydantic==1.10.17

# File handling
aiofiles==23.2.1

# Image processing (for Load Image functionality)
Pillow==10.2.0

# Utilities
requests==2.31.0
python-dateutil==2.8.2

# Development and testing (optional)
pytest==8.0.0
pytest-asyncio==0.23.6
httpx==0.25.0

# CORS support
flask-cors==4.0.0


