"""Application configuration settings"""

import os
from typing import List, Optional
from pydantic import BaseModel, validator
from dotenv import load_dotenv

load_dotenv()

class Settings(BaseModel):
    """Application settings"""

    # API Settings
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "NOAH Arch API"
    VERSION: str = "1.0.0"

    # CORS Settings - Allow development hosts
    ALLOWED_HOSTS: List[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:5173",
        "http://127.0.0.1:5173",
        "http://**************:5173",
        "http://**************:3000",
        "*"  # Allow all origins for development
    ]

    # Azure OpenAI Settings
    AZURE_OPENAI_API_KEY: Optional[str] = os.getenv("AZURE_OPENAI_API_KEY")
    AZURE_OPENAI_ENDPOINT: Optional[str] = os.getenv("AZURE_OPENAI_ENDPOINT")
    AZURE_OPENAI_DEPLOYMENT_NAME: Optional[str] = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME")
    AZURE_OPENAI_API_VERSION: str = os.getenv("AZURE_OPENAI_API_VERSION", "2023-07-01-preview")

    # Azure OpenAI GPT-4o Vision Settings (for Load Image functionality)
    AZURE_OPENAI_VISION_API_KEY: Optional[str] = os.getenv("AZURE_OPENAI_VISION_API_KEY")
    AZURE_OPENAI_VISION_ENDPOINT: Optional[str] = os.getenv("AZURE_OPENAI_VISION_ENDPOINT")
    AZURE_OPENAI_VISION_DEPLOYMENT_NAME: Optional[str] = os.getenv("AZURE_OPENAI_VISION_DEPLOYMENT_NAME", "gpt-4o")
    AZURE_OPENAI_VISION_API_VERSION: str = os.getenv("AZURE_OPENAI_VISION_API_VERSION", "2024-02-15-preview")
    AZURE_OPENAI_TIMEOUT_SECONDS: int = int(os.getenv("AZURE_OPENAI_TIMEOUT_SECONDS", "60"))

    # Pinecone Settings
    PINECONE_API_KEY: Optional[str] = os.getenv("PINECONE_API_KEY")
    PINECONE_ENVIRONMENT: Optional[str] = os.getenv("PINECONE_ENVIRONMENT")
    PINECONE_INDEX_NAME: str = "index384"

    # ML Model Paths (relative to MapleGUI directory)
    ML_MODELS_PATH: str = "../MapleGUI"
    LAMBDA_MODEL_PATH: str = "lambda_model.joblib"
    LAMBDA_SCALER_PATH: str = "lambda_scaler.joblib"
    LAMBDA_ENCODER_PATH: str = "lambda_encoder.joblib"
    S3_MODEL_PATH: str = "s3_model.pkl"
    S3_PREPROCESSOR_PATH: str = "s3_preprocessor.pkl"
    DYNAMO_MODEL_PATH: str = "dynamo_model.joblib"
    DYNAMO_SCALER_PATH: str = "dynamo_scaler.joblib"
    XGB_MODEL_PATH: str = "best_xgb_model_2.pkl"

    # Vector Database Settings
    CHROMA_PERSIST_DIRECTORY: str = "../MapleGUI/noah_db1"

    # Database Settings
    DATABASE_URL: str = "sqlite:///./noah_arch.db"
    DATABASE_ECHO: bool = False  # Set to True for SQL query logging

    # Authentication Settings
    SECRET_KEY: str = "your-secret-key-change-this-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # File Upload Settings
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    ALLOWED_FILE_TYPES: List[str] = [".pdf", ".docx", ".txt"]
    UPLOAD_DIRECTORY: str = "uploads"

    # Image Upload Settings (for Load Image functionality)
    MAX_IMAGE_SIZE: int = 20 * 1024 * 1024  # 20MB for images
    ALLOWED_IMAGE_TYPES: List[str] = [".png", ".jpg", ".jpeg"]
    IMAGE_UPLOAD_DIRECTORY: str = "uploads/images"

    # Cost Calculation Constants (matching original PyQt5 app exactly)
    LAMBDA_COST_RATE_GBSEC: float = 0.00001667
    LAMBDA_COST_RATE_INVOCATION: float = 0.0000002
    # DynamoDB constants - calculated exactly like PyQt5 dynamodb_makespan_cost.py
    DYNAMODB_WRITE_COST_PER_REQUEST: float = 1.25 / 1000000  # Exact PyQt5 calculation
    DYNAMODB_STORAGE_COST_PER_GB_MONTH: float = 1.25  # Exact PyQt5 value

    # Timeout Settings
    REQUEST_TIMEOUT_SECONDS: int = 300  # 5 minutes default timeout
    AZURE_OPENAI_TIMEOUT_SECONDS: int = 120  # 2 minutes for Azure OpenAI calls
    ARCHITECTURE_GENERATION_TIMEOUT_SECONDS: int = 600  # 10 minutes for architecture generation

    class Config:
        """Pydantic v1 configuration"""
        env_file = ".env"
        case_sensitive = True

    @validator("ALLOWED_HOSTS", pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v


settings = Settings()

