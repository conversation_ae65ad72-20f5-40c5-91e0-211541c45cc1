"""
Image Processing Service for Load Image functionality

This service replicates the PyQt5 MapleGUI Load Image functionality,
providing AI-powered architecture diagram analysis using Azure OpenAI GPT-4o.
"""

import base64
import os
import tempfile
import uuid
from typing import Dict, List, Any, Optional, Tuple
import asyncio
import logging
from fastapi import UploadFile, HTTPException

from openai import AsyncAzureOpenAI
from app.core.config import settings

logger = logging.getLogger(__name__)


class ImageProcessingService:
    """
    Service for processing architecture diagram images using Azure OpenAI GPT-4o
    
    Replicates the exact functionality from PyQt5 MapleGUI Diagram.py loadImage() method
    """
    
    def __init__(self):
        """Initialize the image processing service with Azure OpenAI client"""
        self.azure_client = None
        self.aws_services_list = self._get_aws_services_list()
        
        # Initialize Azure OpenAI client for vision processing
        if (settings.AZURE_OPENAI_VISION_API_KEY and 
            settings.AZURE_OPENAI_VISION_ENDPOINT):
            try:
                self.azure_client = AsyncAzureOpenAI(
                    api_key=settings.AZURE_OPENAI_VISION_API_KEY,
                    api_version=settings.AZURE_OPENAI_VISION_API_VERSION,
                    azure_endpoint=settings.AZURE_OPENAI_VISION_ENDPOINT,
                    timeout=settings.AZURE_OPENAI_TIMEOUT_SECONDS,
                    max_retries=2
                )
                logger.info("Azure OpenAI Vision client initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Azure OpenAI Vision client: {e}")
                self.azure_client = None
        else:
            logger.warning("Azure OpenAI Vision credentials not configured")

    async def process_architecture_image(self, file: UploadFile) -> Dict[str, Any]:
        """
        Process an architecture diagram image and extract services and connections
        
        This method replicates the exact PyQt5 MapleGUI loadImage() workflow:
        1. Image upload and base64 encoding
        2. Three-stage AI analysis (services, counts, connections)
        3. Service validation against AWS catalog
        4. Architecture generation
        
        Args:
            file: Uploaded image file (PNG/JPG/JPEG)
            
        Returns:
            Dictionary containing processed architecture data
        """
        if not self.azure_client:
            raise HTTPException(
                status_code=500,
                detail="Azure OpenAI Vision service not configured"
            )
        
        try:
            # Step 1: Validate and process image file
            await self._validate_image_file(file)
            
            # Step 2: Convert image to base64 (exact PyQt5 approach)
            encoded_image = await self._encode_image_to_base64(file)
            
            # Step 3: Three-stage AI analysis (exact PyQt5 prompts)
            logger.info("Starting three-stage AI analysis")
            
            # Stage 1: Service identification
            services = await self._identify_services(encoded_image)
            logger.info(f"Identified {len(services)} services: {services}")
            
            # Stage 2: Service count detection
            service_counts = await self._count_services(encoded_image, services)
            logger.info(f"Service counts: {service_counts}")
            
            # Stage 3: Connection analysis
            connections = await self._analyze_connections(encoded_image, service_counts)
            logger.info(f"Identified {len(connections)} connections")
            
            # Step 4: Generate architecture data
            architecture_data = self._generate_architecture_data(service_counts, connections)
            
            return {
                'status': 'success',
                'message': 'Image processed successfully',
                'data': architecture_data,
                'metadata': {
                    'services_identified': len(services),
                    'total_service_instances': len(service_counts),
                    'connections_found': len(connections),
                    'filename': file.filename
                }
            }
            
        except Exception as e:
            logger.error(f"Error processing architecture image: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'message': 'Failed to process architecture image'
            }

    async def _validate_image_file(self, file: UploadFile) -> None:
        """Validate uploaded image file"""
        if not file.filename:
            raise HTTPException(status_code=400, detail="No file provided")

        # Check file extension
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in settings.ALLOWED_IMAGE_TYPES:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported file type. Allowed types: {settings.ALLOWED_IMAGE_TYPES}"
            )

        # Check file size - handle both UploadFile and mock objects
        if hasattr(file, 'file'):
            # Real UploadFile object
            file.file.seek(0, 2)  # Seek to end
            file_size = file.file.tell()
            file.file.seek(0)  # Reset to beginning
        elif hasattr(file, 'size'):
            # Mock file object with size attribute
            file_size = file.size
        else:
            # Fallback: read content to get size
            content = await file.read()
            file_size = len(content)
            # Reset file position if possible
            if hasattr(file, 'seek'):
                file.seek(0)

        if file_size > settings.MAX_IMAGE_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"File too large. Maximum size: {settings.MAX_IMAGE_SIZE / (1024*1024):.1f}MB"
            )

    async def _encode_image_to_base64(self, file: UploadFile) -> str:
        """
        Convert uploaded image to base64 encoding
        
        Replicates PyQt5 approach:
        encoded_image = base64.b64encode(open(fileName, 'rb').read()).decode('ascii')
        """
        try:
            # Read file content
            file_content = await file.read()
            
            # Encode to base64
            encoded_image = base64.b64encode(file_content).decode('ascii')
            
            logger.info(f"Image encoded to base64, size: {len(encoded_image)} characters")
            return encoded_image
            
        except Exception as e:
            logger.error(f"Failed to encode image to base64: {e}")
            raise HTTPException(status_code=500, detail="Failed to process image")

    async def _identify_services(self, encoded_image: str) -> List[str]:
        """
        Stage 1: Identify cloud services in the image
        
        Uses exact PyQt5 prompt from Diagram.py lines 804-805
        """
        # Exact PyQt5 prompt structure
        prompt = (
            "List all the cloud services and non cloud inputs present in the diagram. "
            "By non cloud inputs I mean objects that provides inputs by arrows to the cloud services "
            "by line starting from it, or receives output from cloud services by line coming into it "
            "along with a arrow head. Take care to output all non cloud input and output objects. "
            "Do not write any more text. Always in the image there must be an initial input coming "
            "into the cloud architecture and output going out of the could architecture. Always model "
            "this input and output as non cloud service 'User'. So your reply must and must contain this. "
            "Dont miss non cloud services. Do not provide any explanation. Do not write any prefix sentences. "
            "Do not provide numbering of the services. Just write the service names one after the other "
            "seperated by newline only, not comma etc. Predict the unique set of cloud services in the image. "
            "The services you predict, should be always from the valid AWS services list given below. "
            "Do not predict a service that is not in the valid AWS services list. And the text you predict "
            "should be ditto from the valid AWS services list. Do not change a word, or do not shorten "
            f"the text of any service. The list is:\n{chr(10).join(self.aws_services_list)}"
        )
        
        try:
            response = await self._call_vision_api(prompt, encoded_image, max_tokens=500)
            
            # Process response (exact PyQt5 logic)
            services = response.split("\n")
            cleaned_services = []
            
            for service in services:
                service = service.strip()
                if ("cloud services" in service.lower() or 
                    "non-cloud" in service.lower() or 
                    len(service) == 0):
                    continue
                cleaned_services.append(service.replace("- ", "").strip())
            
            return cleaned_services
            
        except Exception as e:
            logger.error(f"Failed to identify services: {e}")
            raise HTTPException(status_code=500, detail="Failed to analyze image services")

    async def _count_services(self, encoded_image: str, services: List[str]) -> List[str]:
        """
        Stage 2: Count occurrences of each service
        
        Uses exact PyQt5 prompt from Diagram.py lines 812-813
        """
        prompt = (
            "For each of the cloud service only in the list, mention the count or number of them "
            "occuring in the image. So basically you have to say for each of the text in the list below, "
            "how many times that text occur in the image. The output format should be each service in "
            "each line, followed by a dash '-' and then its count. Do not write any other text. "
            "Do not write any prefix or suffix text. The list is:\n" + "\n".join(services)
        )
        
        try:
            response = await self._call_vision_api(prompt, encoded_image, max_tokens=500)
            
            # Process response (exact PyQt5 logic from lines 814-821)
            service_counts = []
            for line in response.split("\n"):
                line = line.strip()
                if "-" in line and line:
                    try:
                        service_name = line.split("-")[0].strip()
                        count = int(line.split("-")[-1].strip())
                        
                        if count == 1:
                            service_counts.append(service_name)
                        else:
                            for i in range(count):
                                service_counts.append(f"{service_name}_{i}")
                    except (ValueError, IndexError):
                        # Skip malformed lines
                        continue
            
            return service_counts
            
        except Exception as e:
            logger.error(f"Failed to count services: {e}")
            raise HTTPException(status_code=500, detail="Failed to count services")

    async def _analyze_connections(self, encoded_image: str, service_counts: List[str]) -> List[str]:
        """
        Stage 3: Analyze connections between services
        
        Uses exact PyQt5 prompt from Diagram.py lines 822-823
        """
        # Exact PyQt5 prompt structure (very long prompt from original)
        prompt = (
            "The services in the diagram are directly connected by a single arrow, originating from it, "
            "in 1 step to 1 other service in the diagram. Remember that there is always only a single "
            "target service. By an arrow originating from one service, I mean that the tail of the arrow "
            "(not having the arrow head), is attached to the other service. While the head of the arrow "
            "containing the arrow head in attached to those services or box in the diagram. Be very very "
            "careful about the direction of arrows. One service may have multiple arrows originating from it. "
            "I want a list of all such arrows in the image, outputted as pairs of services. A service can "
            "occur multiple times in the image. For that, the list will have repetitions of the same servive "
            "followed by an underscore and a unique count. So match a service in the image with a service "
            "followed by an underscore and respective counter in the list. Thus while outputting the service "
            "pairs take care to check which service followed by underscore and counter is part of which arrow. "
            "Dont mix this up. For example Amazon EC2, if there are multiple EC2, make sure you note from "
            "which EC2, each arrow is originating from or ending to. Take care and mention all of them. "
            "Always in the image there must be an initial input coming into the cloud architecture and output "
            "going out of the could architecture. This input and out is always the 'User' service in the list, "
            "maybe followed by underscore and counter. So the must and absolutely must be an input arrow in "
            "your replies from one of the 'User' service and an output to the same or other 'User' service. "
            "Do not write any more text or dashes or numbers. Do not provide any explanation. I do not want "
            "to know anything extra. Just output the respective input service followed by '-' and followed "
            "by target services. Each and every service mentioned in the list must feature in your edges list. "
            "Dont miss anyone. Dont miss even a service which is mentioned multiple times followed by underscore "
            "and counter. For example: if there is Amazon EC2_0 and Amazon EC2_1 in the list. Both must and "
            "definitely must feature in your output. Dont miss one or more of them. Take very close care that, "
            "the graph you thus output, should be fully connected. This means that, in the graph you output, "
            "ther should always be a path from the starting service which is always User_0. Take very good "
            f"care of this. The complete service list is: {str(service_counts)}"
        )
        
        try:
            response = await self._call_vision_api(prompt, encoded_image, max_tokens=1000)
            
            # Process response (exact PyQt5 logic from lines 824-827)
            connections = []
            for line in response.split("\n"):
                line = line.strip()
                if "-" in line and line:
                    connections.append(line)
            
            return connections
            
        except Exception as e:
            logger.error(f"Failed to analyze connections: {e}")
            raise HTTPException(status_code=500, detail="Failed to analyze connections")

    async def _call_vision_api(self, prompt: str, encoded_image: str, max_tokens: int = 500) -> str:
        """
        Call Azure OpenAI GPT-4o Vision API

        Replicates PyQt5 getresponse() function from Diagram.py lines 788-802
        """
        try:
            response = await self.azure_client.chat.completions.create(
                model=settings.AZURE_OPENAI_VISION_DEPLOYMENT_NAME,
                messages=[
                    {
                        "role": "user",
                        "content": "You are an expert AWS cloud architect. You can see an image of a cloud architechture and read and comprehend it correctly. You can also identify multiple occurances of a cloud service in the image."
                    },
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{encoded_image}"}}
                        ]
                    }
                ],
                max_tokens=max_tokens
            )

            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"Azure OpenAI Vision API call failed: {e}")
            raise HTTPException(status_code=500, detail=f"AI analysis failed: {str(e)}")

    def _generate_architecture_data(self, service_counts: List[str], connections: List[str]) -> Dict[str, Any]:
        """
        Generate architecture data compatible with React Flow

        Converts PyQt5 service list and connections to web application format
        """
        try:
            # Parse connections into edges list (PyQt5 logic from plotGraph)
            edges_list = []
            for connection in connections:
                if "-" in connection:
                    parts = connection.split("-", 1)
                    if len(parts) == 2:
                        source = parts[0].strip()
                        target = parts[1].strip()
                        edges_list.append((source, target))

            # Create adjacency list (PyQt5 logic from edges_to_adjacency_list)
            adjacency_list = self._create_adjacency_list(edges_list)

            # Generate nodes and edges for React Flow
            nodes = self._generate_nodes(service_counts, adjacency_list)
            edges = self._generate_edges(edges_list)

            return {
                'nodes': nodes,
                'edges': edges,
                'adjacency_list': adjacency_list,
                'service_counts': service_counts,
                'connections': connections
            }

        except Exception as e:
            logger.error(f"Failed to generate architecture data: {e}")
            raise HTTPException(status_code=500, detail="Failed to generate architecture")

    def _create_adjacency_list(self, edges_list: List[Tuple[str, str]]) -> Dict[str, List[str]]:
        """
        Create adjacency list from edges

        Replicates PyQt5 edges_to_adjacency_list logic from Diagram.py lines 638-713
        """
        adjacency_list = {}
        in_degree = {}

        # Build initial adjacency list and in-degree count
        for source, target in edges_list:
            if source not in adjacency_list:
                adjacency_list[source] = []
                in_degree[source] = 0

            if target not in adjacency_list:
                adjacency_list[target] = []
                in_degree[target] = 0

            # Prevent self-loops
            if source == target:
                continue

            # Add edge if it doesn't create a duplicate
            if target not in adjacency_list[source]:
                adjacency_list[source].append(target)
                in_degree[target] += 1

        return adjacency_list

    def _generate_nodes(self, service_counts: List[str], adjacency_list: Dict[str, List[str]]) -> List[Dict[str, Any]]:
        """Generate React Flow nodes from service list"""
        nodes = []

        # Starting position (similar to PyQt5 plotOnCanvas2)
        current_x = 160
        current_y = 160
        x_offset = 150
        y_offset = 150

        for i, service_name in enumerate(service_counts):
            # Clean service name (remove _0, _1 suffixes)
            clean_name = service_name.split("_")[0]

            # Map to web application service format
            service_data = self._map_service_to_web_format(clean_name)

            # Calculate position
            x = current_x + (i % 4) * x_offset
            y = current_y + (i // 4) * y_offset

            node = {
                'id': service_name,
                'type': 'serviceNode',
                'position': {'x': x, 'y': y},
                'data': {
                    'service': service_data,
                    'config': service_data.get('defaultConfig', {}),
                    'label': clean_name.replace("Amazon ", "").replace("AWS ", ""),
                    'cost': 0,
                    'latency': 0
                },
                # Add all required React Flow properties
                'selected': False,
                'dragging': False,
                'width': 64,
                'height': 64,
                'zIndex': 0,
                'hidden': False,
                'deletable': True,
                'selectable': True,
                'connectable': True,
                'focusable': True
            }

            nodes.append(node)

        return nodes

    def _generate_edges(self, edges_list: List[Tuple[str, str]]) -> List[Dict[str, Any]]:
        """Generate React Flow edges from connections"""
        edges = []

        for i, (source, target) in enumerate(edges_list):
            edge = {
                'id': f'edge-{i}',
                'source': source,
                'target': target,
                'type': 'step',
                'markerEnd': {
                    'type': 'arrowclosed',
                    'color': '#4F46E5',
                    'width': 20,
                    'height': 20
                },
                'style': {
                    'stroke': '#4F46E5',
                    'strokeWidth': 2.5
                },
                # Add all required React Flow edge properties
                'animated': False,
                'sourceHandle': None,
                'targetHandle': None,
                'selected': False,
                'hidden': False,
                'deletable': True,
                'selectable': True,
                'focusable': True
            }
            edges.append(edge)

        return edges

    def _map_service_to_web_format(self, service_name: str) -> Dict[str, Any]:
        """
        Map PyQt5 service name to web application service format

        Uses the same AWS services list as the frontend serviceDefinitions.ts
        """
        # Service mapping based on PyQt5 awslist and web app service definitions
        service_mapping = {
            'User': {
                'id': 'user',
                'name': 'User',
                'provider': 'AWS',
                'category': 'User',
                'icon': '👤',
                'fallbackIcon': '👤',
                'description': 'End users or clients accessing the system',
                'defaultConfig': {'userCount': 1000, 'location': 'Global'},
                'color': '#8B5CF6'
            },
            'AWS Lambda': {
                'id': 'aws-lambda',
                'name': 'AWS Lambda',
                'provider': 'AWS',
                'category': 'Compute',
                'icon': '/icons/aws/AWS Lambda.png',
                'fallbackIcon': '⚡',
                'description': 'Serverless compute service',
                'defaultConfig': {
                    'workload_invocations': 1000,
                    'memory_mb': 1024,
                    'function_purpose': 'DTS_deepreader',
                    'memory_required': 204
                },
                'color': '#FF9900'
            },
            'Amazon S3': {
                'id': 'aws-s3',
                'name': 'Amazon Simple Storage System (S3)',
                'provider': 'AWS',
                'category': 'Storage',
                'icon': '/icons/aws/Amazon Simple Storage System (S3).png',
                'fallbackIcon': '🪣',
                'description': 'Object storage service',
                'defaultConfig': {
                    'workload': 100,
                    'file_size': 1024,
                    'memory': 512,
                    'operation': 'read'
                },
                'color': '#FF9900'
            },
            'Amazon DynamoDB': {
                'id': 'aws-dynamodb',
                'name': 'Amazon DynamoDB',
                'provider': 'AWS',
                'category': 'Database',
                'icon': '/icons/aws/Amazon DynamoDB.png',
                'fallbackIcon': '🗄️',
                'description': 'NoSQL database service',
                'defaultConfig': {
                    'workload': 1,
                    'read_capacity': 5,
                    'write_capacity': 5,
                    'storage_gb': 10
                },
                'color': '#FF9900'
            },
            'Amazon EC2': {
                'id': 'aws-ec2',
                'name': 'Amazon EC2',
                'provider': 'AWS',
                'category': 'Compute',
                'icon': '/icons/aws/Amazon EC2.png',
                'fallbackIcon': '🖥️',
                'description': 'Virtual servers in the cloud',
                'defaultConfig': {
                    'instanceType': 'Inferentia(Inf2.24xlarge)',
                    'LLMModel': 'llama_model_7b',
                    'batchSize': '1',
                    'inputTokens': '50',
                    'outputTokens': '150',
                    'workload': 1000
                },
                'color': '#FF9900'
            },
            'Amazon API Gateway': {
                'id': 'aws-api-gateway',
                'name': 'Amazon API Gateway',
                'provider': 'AWS',
                'category': 'Networking',
                'icon': '/icons/aws/Amazon API Gateway.png',
                'fallbackIcon': '🌐',
                'description': 'API management service',
                'defaultConfig': {
                    'requests_per_hour': 1000,
                    'input_payload_size_kb': 10,
                    'output_payload_size_kb': 10
                },
                'color': '#FF9900'
            }
        }

        # Return mapped service or create default
        if service_name in service_mapping:
            return service_mapping[service_name]
        else:
            # Create default service for unknown services
            clean_name = service_name.replace("Amazon ", "").replace("AWS ", "")
            return {
                'id': clean_name.lower().replace(" ", "-"),
                'name': service_name,
                'provider': 'AWS',
                'category': 'Other',
                'icon': '/icons/maple.png',
                'fallbackIcon': '☁️',
                'description': f'{service_name} service',
                'defaultConfig': {'workload': 1000},
                'color': '#FF9900'
            }

    def _get_aws_services_list(self) -> List[str]:
        """
        Get AWS services list compatible with PyQt5 awslist

        This should match the services from MapleGUI/globalslist.py
        """
        return [
            "User",  # Special case - always preserve User
            "Amazon API Gateway",
            "Amazon AppFlow",
            "Amazon AppStream 2.0",
            "Amazon AppSync",
            "Amazon Athena",
            "Amazon Aurora",
            "Amazon CloudFront",
            "Amazon CloudWatch",
            "Amazon Cognito",
            "Amazon Connect",
            "Amazon DocumentDB",
            "Amazon DynamoDB",
            "Amazon EC2",
            "Amazon ECS",
            "Amazon EKS",
            "Amazon ElastiCache",
            "Amazon Elasticsearch Service",
            "Amazon EMR",
            "Amazon EventBridge",
            "Amazon Fargate",
            "Amazon FSx",
            "Amazon GameLift",
            "Amazon GuardDuty",
            "Amazon Inspector",
            "Amazon Kinesis",
            "Amazon Lambda",
            "Amazon Lex",
            "Amazon Lightsail",
            "Amazon MQ",
            "Amazon Neptune",
            "Amazon Polly",
            "Amazon QuickSight",
            "Amazon RDS",
            "Amazon Redshift",
            "Amazon Rekognition",
            "Amazon Route 53",
            "Amazon S3",
            "Amazon SageMaker",
            "Amazon Simple Storage System (S3)",
            "Amazon SNS",
            "Amazon SQS",
            "Amazon Textract",
            "Amazon Translate",
            "Amazon VPC",
            "AWS Batch",
            "AWS CloudFormation",
            "AWS CloudTrail",
            "AWS CodeBuild",
            "AWS CodeCommit",
            "AWS CodeDeploy",
            "AWS CodePipeline",
            "AWS Config",
            "AWS Direct Connect",
            "AWS Elastic Beanstalk",
            "AWS Glue",
            "AWS IAM",
            "AWS IoT Core",
            "AWS Key Management Service",
            "AWS Lambda",
            "AWS Load Balancer",
            "AWS Organizations",
            "AWS Secrets Manager",
            "AWS Step Functions",
            "AWS Systems Manager",
            "AWS WAF",
            "AWS X-Ray"
        ]

    async def get_processing_status(self, task_id: str) -> Dict[str, Any]:
        """Get the status of an image processing task"""
        # This would be implemented with a task queue system like Celery
        # For now, return a simple status
        return {
            'task_id': task_id,
            'status': 'completed',
            'progress': 100,
            'message': 'Image processing completed'
        }

    def get_aws_services_catalog(self) -> List[str]:
        """Get the complete AWS services catalog for validation"""
        return self.aws_services_list
