#!/usr/bin/env python3
"""
Test script to verify the edge rendering fix

This script tests the updated image processing service to ensure
edges are properly generated with all required React Flow properties.
"""

import asyncio
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from app.services.image_processing_service import ImageProcessingService


async def test_edge_generation_fix():
    """Test the updated edge generation with all React Flow properties"""
    print("🔧 Testing Edge Generation Fix")
    print("=" * 50)
    
    service = ImageProcessingService()
    
    # Test data that matches your actual response
    test_service_counts = [
        "User_0",
        "Amazon EC2_0", 
        "Amazon DynamoDB_0",
        "AWS CloudTrail_0",
        "AWS Lambda_0",
        "User_1"
    ]
    
    test_connections = [
        "User_0-Amazon EC2_0",
        "Amazon EC2_0-Amazon DynamoDB_0",
        "Amazon DynamoDB_0-AWS CloudTrail_0", 
        "AWS CloudTrail_0-AWS Lambda_0",
        "AWS Lambda_0-User_1"
    ]
    
    print(f"📊 Testing with {len(test_service_counts)} services and {len(test_connections)} connections")
    
    # Generate architecture data
    try:
        architecture_data = service._generate_architecture_data(test_service_counts, test_connections)
        
        print(f"\n✅ Generated architecture data successfully")
        print(f"   Nodes: {len(architecture_data['nodes'])}")
        print(f"   Edges: {len(architecture_data['edges'])}")
        
        # Verify nodes have all required properties
        print(f"\n🔍 Verifying Node Properties:")
        for i, node in enumerate(architecture_data['nodes']):
            print(f"   Node {i+1}: {node['id']}")
            print(f"     - Type: {node.get('type', 'MISSING')}")
            print(f"     - Position: {node.get('position', 'MISSING')}")
            print(f"     - Selectable: {node.get('selectable', 'MISSING')}")
            print(f"     - Connectable: {node.get('connectable', 'MISSING')}")
            print(f"     - Label: {node.get('data', {}).get('label', 'MISSING')}")
            
            # Check for missing required properties
            required_props = ['id', 'type', 'position', 'data', 'selectable', 'connectable', 'deletable']
            missing_props = [prop for prop in required_props if prop not in node]
            if missing_props:
                print(f"     ❌ Missing properties: {missing_props}")
            else:
                print(f"     ✅ All required properties present")
        
        # Verify edges have all required properties
        print(f"\n🔗 Verifying Edge Properties:")
        for i, edge in enumerate(architecture_data['edges']):
            print(f"   Edge {i+1}: {edge['id']} ({edge['source']} -> {edge['target']})")
            print(f"     - Type: {edge.get('type', 'MISSING')}")
            print(f"     - MarkerEnd: {edge.get('markerEnd', 'MISSING')}")
            print(f"     - Style: {edge.get('style', 'MISSING')}")
            print(f"     - Selectable: {edge.get('selectable', 'MISSING')}")
            print(f"     - Animated: {edge.get('animated', 'MISSING')}")
            
            # Check for missing required properties
            required_props = ['id', 'source', 'target', 'type', 'markerEnd', 'style', 'selectable']
            missing_props = [prop for prop in required_props if prop not in edge]
            if missing_props:
                print(f"     ❌ Missing properties: {missing_props}")
            else:
                print(f"     ✅ All required properties present")
        
        # Verify node-edge ID consistency
        print(f"\n🔄 Verifying Node-Edge ID Consistency:")
        node_ids = set(node['id'] for node in architecture_data['nodes'])
        edge_sources = set(edge['source'] for edge in architecture_data['edges'])
        edge_targets = set(edge['target'] for edge in architecture_data['edges'])
        
        print(f"   Node IDs: {sorted(node_ids)}")
        print(f"   Edge Sources: {sorted(edge_sources)}")
        print(f"   Edge Targets: {sorted(edge_targets)}")
        
        # Check for orphaned edges (edges pointing to non-existent nodes)
        orphaned_sources = edge_sources - node_ids
        orphaned_targets = edge_targets - node_ids
        
        if orphaned_sources:
            print(f"   ❌ Orphaned edge sources: {orphaned_sources}")
        else:
            print(f"   ✅ All edge sources have corresponding nodes")
            
        if orphaned_targets:
            print(f"   ❌ Orphaned edge targets: {orphaned_targets}")
        else:
            print(f"   ✅ All edge targets have corresponding nodes")
        
        # Generate sample React Flow compatible output
        print(f"\n📋 Sample React Flow Output:")
        print("Nodes:")
        for node in architecture_data['nodes'][:2]:  # Show first 2 nodes
            print(f"  {node}")
        
        print("Edges:")
        for edge in architecture_data['edges'][:2]:  # Show first 2 edges
            print(f"  {edge}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating architecture data: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_service_mapping():
    """Test service mapping for the services in your example"""
    print(f"\n🗺️  Testing Service Mapping")
    print("=" * 50)
    
    service = ImageProcessingService()
    
    test_services = ["User", "Amazon EC2", "Amazon DynamoDB", "AWS CloudTrail", "AWS Lambda"]
    
    for service_name in test_services:
        mapped_service = service._map_service_to_web_format(service_name)
        print(f"   {service_name} -> {mapped_service['name']}")
        print(f"     Icon: {mapped_service.get('icon', 'No icon')}")
        print(f"     Category: {mapped_service.get('category', 'No category')}")
        print(f"     Default Config: {mapped_service.get('defaultConfig', {})}")


async def main():
    """Main test function"""
    print("🚀 Edge Generation Fix Test Suite")
    print("=" * 60)
    
    # Test edge generation fix
    success = await test_edge_generation_fix()
    
    # Test service mapping
    await test_service_mapping()
    
    if success:
        print(f"\n🎉 All tests passed! The edge rendering issue should be fixed.")
        print(f"\nNext steps:")
        print(f"1. Restart your FastAPI backend server")
        print(f"2. Test the Load Image functionality in the frontend")
        print(f"3. Check browser console for the detailed logging")
        print(f"4. Verify that edges now appear in the React Flow canvas")
    else:
        print(f"\n❌ Tests failed. Please check the error messages above.")


if __name__ == "__main__":
    asyncio.run(main())
