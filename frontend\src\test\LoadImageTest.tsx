import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import LoadImageDialog from '@/components/architecture/LoadImageDialog'
import { imageProcessingService } from '@/services/imageProcessingService'

/**
 * Test component for Load Image functionality
 * 
 * This component provides a simple interface to test the Load Image dialog
 * and image processing service without needing the full architecture designer.
 */
const LoadImageTest: React.FC = () => {
  const [showDialog, setShowDialog] = useState(false)
  const [testResults, setTestResults] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const addTestResult = (test: string, result: 'success' | 'error', message: string, data?: any) => {
    setTestResults(prev => [...prev, {
      test,
      result,
      message,
      data,
      timestamp: new Date().toLocaleTimeString()
    }])
  }

  const testImageProcessingService = async () => {
    setIsLoading(true)
    addTestResult('Service Test', 'success', 'Starting image processing service tests...')

    try {
      // Test 1: File validation
      const testFile = new File(['test'], 'test.png', { type: 'image/png' })
      const validation = imageProcessingService.validateImageFile(testFile)
      addTestResult(
        'File Validation', 
        validation.valid ? 'success' : 'error',
        validation.valid ? 'File validation passed' : validation.error || 'Validation failed'
      )

      // Test 2: Health check
      try {
        const health = await imageProcessingService.checkHealth()
        addTestResult('Health Check', 'success', 'Service health check passed', health)
      } catch (error) {
        addTestResult('Health Check', 'error', `Health check failed: ${error}`)
      }

      // Test 3: AWS Services Catalog
      try {
        const services = await imageProcessingService.getAWSServicesCatalog()
        addTestResult(
          'AWS Services', 
          'success', 
          `Retrieved ${services.total_count} AWS services`,
          { sample: services.services.slice(0, 5) }
        )
      } catch (error) {
        addTestResult('AWS Services', 'error', `Failed to get AWS services: ${error}`)
      }

      // Test 4: Service Validation
      try {
        const testServices = ['AWS Lambda', 'Amazon S3', 'Invalid Service']
        const validation = await imageProcessingService.validateServices(testServices)
        addTestResult(
          'Service Validation',
          'success',
          `Validated ${validation.validation_summary.total_services} services`,
          validation
        )
      } catch (error) {
        addTestResult('Service Validation', 'error', `Service validation failed: ${error}`)
      }

    } catch (error) {
      addTestResult('Service Test', 'error', `Test suite failed: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }

  const handleArchitectureLoaded = (architectureData: any) => {
    addTestResult(
      'Architecture Loaded',
      'success',
      `Architecture loaded with ${architectureData.nodes?.length || 0} nodes and ${architectureData.edges?.length || 0} edges`,
      {
        nodes: architectureData.nodes?.length || 0,
        edges: architectureData.edges?.length || 0,
        services: architectureData.service_counts || []
      }
    )
  }

  const clearResults = () => {
    setTestResults([])
  }

  return (
    <div className="p-8 max-w-6xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🧪 Load Image Functionality Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Test Controls */}
          <div className="flex gap-4">
            <Button
              onClick={() => setShowDialog(true)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Test Load Image Dialog
            </Button>
            <Button
              onClick={testImageProcessingService}
              disabled={isLoading}
              variant="outline"
            >
              {isLoading ? 'Testing...' : 'Test Image Service'}
            </Button>
            <Button
              onClick={clearResults}
              variant="outline"
            >
              Clear Results
            </Button>
          </div>

          {/* Test Results */}
          {testResults.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Test Results</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {testResults.map((result, index) => (
                    <div
                      key={index}
                      className={`p-3 rounded-lg border ${
                        result.result === 'success'
                          ? 'bg-green-50 border-green-200'
                          : 'bg-red-50 border-red-200'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className={`text-sm font-medium ${
                            result.result === 'success' ? 'text-green-700' : 'text-red-700'
                          }`}>
                            {result.result === 'success' ? '✅' : '❌'} {result.test}
                          </span>
                          <span className="text-xs text-gray-500">
                            {result.timestamp}
                          </span>
                        </div>
                      </div>
                      <p className={`text-sm mt-1 ${
                        result.result === 'success' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {result.message}
                      </p>
                      {result.data && (
                        <details className="mt-2">
                          <summary className="text-xs text-gray-500 cursor-pointer">
                            View Details
                          </summary>
                          <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
                            {JSON.stringify(result.data, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Instructions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Test Instructions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div>
                <strong>1. Test Load Image Dialog:</strong>
                <p>Opens the image upload dialog. Test drag-and-drop, file selection, and processing simulation.</p>
              </div>
              <div>
                <strong>2. Test Image Service:</strong>
                <p>Tests the backend image processing service endpoints (requires backend server running).</p>
              </div>
              <div>
                <strong>3. Backend Requirements:</strong>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>FastAPI server running on http://localhost:8000</li>
                  <li>Azure OpenAI credentials configured</li>
                  <li>JWT authentication token (for full testing)</li>
                </ul>
              </div>
              <div>
                <strong>4. Expected Results:</strong>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>File validation should pass for PNG/JPG files</li>
                  <li>Health check should show service status</li>
                  <li>AWS services catalog should return 60+ services</li>
                  <li>Service validation should identify valid/invalid services</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>

      {/* Load Image Dialog */}
      <LoadImageDialog
        open={showDialog}
        onOpenChange={setShowDialog}
        onArchitectureLoaded={handleArchitectureLoaded}
      />
    </div>
  )
}

export default LoadImageTest
