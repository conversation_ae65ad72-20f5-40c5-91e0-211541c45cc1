import React, { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent } from '@/components/ui/card'
import { Upload, Image, FileImage, X, CheckCircle, AlertCircle, Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useToast } from '@/hooks/use-toast'

interface LoadImageDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onArchitectureLoaded: (architectureData: any) => void
}

interface ProcessingStatus {
  stage: 'upload' | 'encoding' | 'services' | 'counts' | 'connections' | 'generation' | 'complete'
  progress: number
  message: string
}

const PROCESSING_STAGES = [
  { key: 'upload', label: 'Uploading Image', progress: 10 },
  { key: 'encoding', label: 'Encoding Image', progress: 20 },
  { key: 'services', label: 'Identifying Services', progress: 40 },
  { key: 'counts', label: 'Counting Instances', progress: 60 },
  { key: 'connections', label: 'Analyzing Connections', progress: 80 },
  { key: 'generation', label: 'Generating Architecture', progress: 90 },
  { key: 'complete', label: 'Complete', progress: 100 }
]

export const LoadImageDialog: React.FC<LoadImageDialogProps> = ({
  open,
  onOpenChange,
  onArchitectureLoaded
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [processingStatus, setProcessingStatus] = useState<ProcessingStatus>({
    stage: 'upload',
    progress: 0,
    message: 'Ready to upload'
  })
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (file) {
      setSelectedFile(file)
      setError(null)
      
      // Create preview URL
      const url = URL.createObjectURL(file)
      setPreviewUrl(url)
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/png': ['.png'],
      'image/jpeg': ['.jpg', '.jpeg']
    },
    maxFiles: 1,
    maxSize: 20 * 1024 * 1024, // 20MB
    onDropRejected: (fileRejections) => {
      const rejection = fileRejections[0]
      if (rejection) {
        const error = rejection.errors[0]
        setError(error?.message || 'File rejected')
      }
    }
  })

  const simulateProcessingStages = async () => {
    for (const stage of PROCESSING_STAGES) {
      setProcessingStatus({
        stage: stage.key as any,
        progress: stage.progress,
        message: stage.label
      })
      
      // Simulate processing time
      if (stage.key !== 'complete') {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }
  }

  const handleProcessImage = async () => {
    if (!selectedFile) return

    setIsProcessing(true)
    setError(null)

    try {
      // Start processing simulation
      const processingPromise = simulateProcessingStages()

      // Prepare form data
      const formData = new FormData()
      formData.append('file', selectedFile)

      // Call API
      const response = await fetch('/api/v1/image-processing/load-image', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        },
        body: formData
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || 'Failed to process image')
      }

      const result = await response.json()

      // Wait for processing simulation to complete
      await processingPromise

      // Handle successful response
      if (result.status === 'success' && result.data) {
        toast({
          title: "Image Processed Successfully",
          description: `Identified ${result.metadata?.services_identified || 0} services with ${result.metadata?.connections_found || 0} connections`,
        })

        // Pass architecture data to parent
        onArchitectureLoaded(result.data)
        
        // Close dialog
        handleClose()
      } else {
        throw new Error(result.message || 'Processing failed')
      }

    } catch (err) {
      console.error('Image processing error:', err)
      setError(err instanceof Error ? err.message : 'Failed to process image')
      setProcessingStatus({
        stage: 'upload',
        progress: 0,
        message: 'Processing failed'
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const handleClose = () => {
    setSelectedFile(null)
    setPreviewUrl(null)
    setIsProcessing(false)
    setError(null)
    setProcessingStatus({
      stage: 'upload',
      progress: 0,
      message: 'Ready to upload'
    })
    onOpenChange(false)
  }

  const removeFile = () => {
    setSelectedFile(null)
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl)
      setPreviewUrl(null)
    }
    setError(null)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileImage className="h-5 w-5 text-blue-600" />
            Load Architecture from Image
          </DialogTitle>
          <DialogDescription>
            Upload a PNG or JPEG image of your architecture diagram. Our AI will analyze it and recreate the architecture in the designer.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* File Upload Area */}
          {!selectedFile && (
            <div
              {...getRootProps()}
              className={cn(
                "border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors",
                isDragActive ? "border-blue-500 bg-blue-50" : "border-gray-300 hover:border-gray-400"
              )}
            >
              <input {...getInputProps()} />
              <div className="flex flex-col items-center gap-4">
                <div className="p-4 bg-blue-100 rounded-full">
                  <Upload className="h-8 w-8 text-blue-600" />
                </div>
                <div>
                  <p className="text-lg font-medium text-gray-900">
                    {isDragActive ? 'Drop your image here' : 'Upload architecture diagram'}
                  </p>
                  <p className="text-sm text-gray-500 mt-1">
                    Drag and drop or click to select • PNG, JPG up to 20MB
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* File Preview */}
          {selectedFile && previewUrl && (
            <Card>
              <CardContent className="p-4">
                <div className="flex items-start gap-4">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <Image className="h-4 w-4 text-blue-600" />
                        <span className="font-medium text-sm">{selectedFile.name}</span>
                        <span className="text-xs text-gray-500">
                          ({(selectedFile.size / (1024 * 1024)).toFixed(1)} MB)
                        </span>
                      </div>
                      {!isProcessing && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={removeFile}
                          className="h-6 w-6 p-0"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    
                    <div className="border rounded-lg overflow-hidden">
                      <img
                        src={previewUrl}
                        alt="Architecture diagram preview"
                        className="w-full h-48 object-contain bg-gray-50"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Processing Status */}
          {isProcessing && (
            <Card>
              <CardContent className="p-4">
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                    <span className="font-medium">Processing Architecture Diagram</span>
                  </div>
                  
                  <Progress value={processingStatus.progress} className="w-full" />
                  
                  <div className="text-sm text-gray-600">
                    {processingStatus.message}
                  </div>

                  <div className="grid grid-cols-2 gap-2 text-xs">
                    {PROCESSING_STAGES.map((stage) => (
                      <div
                        key={stage.key}
                        className={cn(
                          "flex items-center gap-2 p-2 rounded",
                          processingStatus.stage === stage.key
                            ? "bg-blue-50 text-blue-700"
                            : processingStatus.progress >= stage.progress
                            ? "bg-green-50 text-green-700"
                            : "bg-gray-50 text-gray-500"
                        )}
                      >
                        {processingStatus.progress >= stage.progress ? (
                          <CheckCircle className="h-3 w-3" />
                        ) : processingStatus.stage === stage.key ? (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        ) : (
                          <div className="h-3 w-3 rounded-full border border-current" />
                        )}
                        <span>{stage.label}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button
              onClick={handleProcessImage}
              disabled={!selectedFile || isProcessing}
              className="min-w-[120px]"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Processing...
                </>
              ) : (
                <>
                  <FileImage className="h-4 w-4 mr-2" />
                  Process Image
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default LoadImageDialog
