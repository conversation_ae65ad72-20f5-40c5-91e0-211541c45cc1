"""
Image Processing API endpoints for Load Image functionality

This module provides API endpoints for processing architecture diagram images
using Azure OpenAI GPT-4o, replicating the PyQt5 MapleGUI Load Image feature.
"""

import uuid
from typing import Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, status
from fastapi.responses import JSONResponse

from app.services.image_processing_service import ImageProcessingService
from app.models.responses import SuccessResponse, ErrorResponse
from app.auth.dependencies import get_current_user
from app.database.models import User
from pydantic import BaseModel
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


class ImageProcessingResponse(BaseModel):
    """Response model for image processing"""
    task_id: str
    status: str
    message: str
    data: Dict[str, Any] = None
    metadata: Dict[str, Any] = None


class ProcessingStatusResponse(BaseModel):
    """Response model for processing status"""
    task_id: str
    status: str
    progress: int
    message: str
    data: Dict[str, Any] = None


class AWSServicesResponse(BaseModel):
    """Response model for AWS services catalog"""
    services: List[str]
    total_count: int


def get_image_processing_service() -> ImageProcessingService:
    """Dependency to get image processing service instance"""
    return ImageProcessingService()


@router.post("/load-image", response_model=ImageProcessingResponse)
async def load_architecture_image(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    image_service: ImageProcessingService = Depends(get_image_processing_service)
):
    """
    Load and process architecture diagram image
    
    This endpoint replicates the PyQt5 MapleGUI "Load Image" functionality.
    It accepts PNG/JPG/JPEG files, processes them using Azure OpenAI GPT-4o,
    and returns architecture data compatible with the React Flow frontend.
    
    Process:
    1. Image upload and validation
    2. Base64 encoding
    3. Three-stage AI analysis:
       - Service identification
       - Service count detection  
       - Connection analysis
    4. Architecture data generation
    
    Args:
        file: The architecture diagram image file (PNG/JPG/JPEG)
        current_user: Authenticated user
        
    Returns:
        ImageProcessingResponse with architecture data
    """
    try:
        logger.info(f"User {current_user.username} uploading image: {file.filename}")
        
        # Generate unique task ID
        task_id = str(uuid.uuid4())
        
        # Process the image
        result = await image_service.process_architecture_image(file)
        
        if result['status'] == 'error':
            logger.error(f"Image processing failed for user {current_user.username}: {result['error']}")
            raise HTTPException(
                status_code=400, 
                detail=result['error']
            )
        
        logger.info(f"Image processing completed successfully for user {current_user.username}")
        
        return ImageProcessingResponse(
            task_id=task_id,
            status=result['status'],
            message=result['message'],
            data=result['data'],
            metadata=result.get('metadata', {})
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in load_architecture_image: {e}")
        raise HTTPException(
            status_code=500, 
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/processing-status/{task_id}", response_model=ProcessingStatusResponse)
async def get_processing_status(
    task_id: str,
    current_user: User = Depends(get_current_user),
    image_service: ImageProcessingService = Depends(get_image_processing_service)
):
    """
    Get the status of an image processing task
    
    This endpoint allows clients to check the progress of image processing
    for long-running operations.
    
    Args:
        task_id: Unique task identifier
        current_user: Authenticated user
        
    Returns:
        ProcessingStatusResponse with current status
    """
    try:
        status_data = await image_service.get_processing_status(task_id)
        
        return ProcessingStatusResponse(
            task_id=task_id,
            status=status_data['status'],
            progress=status_data['progress'],
            message=status_data['message'],
            data=status_data.get('data')
        )
        
    except Exception as e:
        logger.error(f"Error getting processing status for task {task_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get processing status: {str(e)}"
        )


@router.get("/aws-services", response_model=AWSServicesResponse)
async def get_aws_services_catalog(
    current_user: User = Depends(get_current_user),
    image_service: ImageProcessingService = Depends(get_image_processing_service)
):
    """
    Get the complete AWS services catalog used for validation
    
    This endpoint returns the AWS services list that matches the PyQt5 MapleGUI
    awslist for service validation during image processing.
    
    Args:
        current_user: Authenticated user
        
    Returns:
        AWSServicesResponse with services catalog
    """
    try:
        services = image_service.get_aws_services_catalog()
        
        return AWSServicesResponse(
            services=services,
            total_count=len(services)
        )
        
    except Exception as e:
        logger.error(f"Error getting AWS services catalog: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get AWS services catalog: {str(e)}"
        )


@router.get("/health")
async def image_processing_health_check(
    image_service: ImageProcessingService = Depends(get_image_processing_service)
):
    """
    Health check for image processing service
    
    Returns the health status of the image processing service and its dependencies.
    """
    try:
        # Check if Azure OpenAI Vision client is available
        azure_vision_status = "healthy" if image_service.azure_client is not None else "not_configured"
        
        overall_status = "healthy" if azure_vision_status == "healthy" else "degraded"
        
        return SuccessResponse(
            message=f"Image processing service status: {overall_status}",
            data={
                "overall_status": overall_status,
                "azure_openai_vision": azure_vision_status,
                "supported_image_types": [".png", ".jpg", ".jpeg"],
                "aws_services_count": len(image_service.aws_services_list)
            }
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return ErrorResponse(
            message="Health check failed",
            error=str(e)
        )


@router.post("/validate-services")
async def validate_services_against_catalog(
    services: List[str],
    current_user: User = Depends(get_current_user),
    image_service: ImageProcessingService = Depends(get_image_processing_service)
):
    """
    Validate a list of services against the AWS catalog
    
    This endpoint validates service names against the official AWS services
    catalog used by the PyQt5 MapleGUI application.
    
    Args:
        services: List of service names to validate
        current_user: Authenticated user
        
    Returns:
        Validation results with valid/invalid services
    """
    try:
        aws_catalog = image_service.get_aws_services_catalog()
        
        valid_services = []
        invalid_services = []
        
        for service in services:
            if service in aws_catalog:
                valid_services.append(service)
            else:
                invalid_services.append(service)
        
        return SuccessResponse(
            message="Service validation completed",
            data={
                "valid_services": valid_services,
                "invalid_services": invalid_services,
                "validation_summary": {
                    "total_services": len(services),
                    "valid_count": len(valid_services),
                    "invalid_count": len(invalid_services)
                }
            }
        )
        
    except Exception as e:
        logger.error(f"Service validation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Service validation failed: {str(e)}"
        )
