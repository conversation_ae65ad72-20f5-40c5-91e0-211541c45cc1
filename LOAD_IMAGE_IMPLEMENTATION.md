# Load Image Functionality Implementation

This document describes the comprehensive implementation of the PyQt5 MapleGUI "Load Image" functionality in the FastAPI + React web application.

## 📋 Overview

The Load Image functionality allows users to upload architecture diagram images (PNG/JPG/JPEG) and automatically convert them into interactive, editable cloud architectures using Azure OpenAI GPT-4o vision capabilities.

## 🏗️ Architecture

### Backend Components

1. **ImageProcessingService** (`backend/app/services/image_processing_service.py`)
   - Core service replicating PyQt5 MapleGUI logic
   - Azure OpenAI GPT-4o integration
   - Three-stage AI analysis pipeline
   - Service validation against AWS catalog
   - Architecture data generation

2. **API Endpoints** (`backend/app/api/v1/endpoints/image_processing.py`)
   - `POST /api/v1/image-processing/load-image` - Main processing endpoint
   - `GET /api/v1/image-processing/processing-status/{task_id}` - Status tracking
   - `GET /api/v1/image-processing/aws-services` - AWS services catalog
   - `POST /api/v1/image-processing/validate-services` - Service validation
   - `GET /api/v1/image-processing/health` - Health check

3. **Configuration** (`backend/app/core/config.py`)
   - Azure OpenAI Vision settings
   - Image upload constraints
   - Processing timeouts

### Frontend Components

1. **LoadImageDialog** (`frontend/src/components/architecture/LoadImageDialog.tsx`)
   - Image upload with drag-and-drop
   - Processing progress visualization
   - Error handling and user feedback
   - Integration with React Flow architecture designer

2. **ImageProcessingService** (`frontend/src/services/imageProcessingService.ts`)
   - API communication layer
   - Data format conversion
   - File validation
   - Error handling

3. **Architecture Designer Integration** (`frontend/src/pages/ArchitectureDesigner.tsx`)
   - Load Image button in main interface
   - Architecture loading from processed image data
   - Seamless integration with existing workflow

## 🔄 Processing Workflow

### 1. Image Upload
- User selects PNG/JPG/JPEG file (max 20MB)
- File validation (type, size)
- Drag-and-drop or click to upload

### 2. Three-Stage AI Analysis

#### Stage 1: Service Identification
```
Prompt: "List all the cloud services and non cloud inputs present in the diagram..."
Output: List of AWS services found in the image
```

#### Stage 2: Service Count Detection
```
Prompt: "For each of the cloud service only in the list, mention the count..."
Output: Service instances with unique identifiers (e.g., "Amazon EC2_0", "Amazon EC2_1")
```

#### Stage 3: Connection Analysis
```
Prompt: "The services in the diagram are directly connected by a single arrow..."
Output: Service-to-service connections with proper directionality
```

### 3. Architecture Generation
- Convert AI analysis to React Flow format
- Generate nodes with proper positioning
- Create edges with arrow styling
- Apply layout optimization

### 4. Integration
- Load architecture into designer canvas
- Enable editing and modification
- Maintain compatibility with save/load functionality

## 🛠️ Configuration

### Environment Variables

```bash
# Azure OpenAI Vision Settings
AZURE_OPENAI_VISION_API_KEY=your_api_key_here
AZURE_OPENAI_VISION_ENDPOINT=https://your-endpoint.openai.azure.com/
AZURE_OPENAI_VISION_DEPLOYMENT_NAME=gpt-4o
AZURE_OPENAI_VISION_API_VERSION=2024-02-15-preview
AZURE_OPENAI_TIMEOUT_SECONDS=60

# Image Upload Settings
MAX_IMAGE_SIZE=20971520  # 20MB
ALLOWED_IMAGE_TYPES=.png,.jpg,.jpeg
IMAGE_UPLOAD_DIRECTORY=uploads/images
```

### Dependencies

#### Backend
```
openai==1.12.0  # Updated for GPT-4o vision support
Pillow==10.2.0  # Image processing
```

#### Frontend
```
react-dropzone==14.2.3  # Already installed
```

## 🧪 Testing

### Run Backend Tests
```bash
cd backend
python test_load_image.py
```

### Test Coverage
- ✅ Image processing service initialization
- ✅ AWS services catalog validation
- ✅ Service mapping to web format
- ✅ Adjacency list creation
- ✅ Node and edge generation
- ✅ Mock image processing workflow
- ✅ Configuration validation

## 🚀 Usage

### 1. Start Backend Server
```bash
cd backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 2. Start Frontend Server
```bash
cd frontend
npm run dev
```

### 3. Access Load Image Feature
1. Navigate to Architecture Designer
2. Click "Load from Image" button
3. Upload architecture diagram image
4. Wait for AI processing
5. Review and edit generated architecture

## 🔧 API Reference

### Load Image Endpoint
```http
POST /api/v1/image-processing/load-image
Content-Type: multipart/form-data

file: <image_file>
```

**Response:**
```json
{
  "task_id": "uuid",
  "status": "success",
  "message": "Image processed successfully",
  "data": {
    "nodes": [...],
    "edges": [...],
    "adjacency_list": {...},
    "service_counts": [...],
    "connections": [...]
  },
  "metadata": {
    "services_identified": 4,
    "total_service_instances": 4,
    "connections_found": 4,
    "filename": "architecture.png"
  }
}
```

## 🎯 Feature Parity with PyQt5 MapleGUI

### ✅ Implemented Features
- [x] Image upload (PNG/JPG/JPEG support)
- [x] Base64 image encoding
- [x] Azure OpenAI GPT-4o integration
- [x] Three-stage AI analysis pipeline
- [x] Service identification with AWS catalog validation
- [x] Service count detection with unique identifiers
- [x] Connection analysis with directionality
- [x] Architecture data generation
- [x] React Flow integration
- [x] Error handling and user feedback
- [x] Processing progress visualization

### 🔄 Exact PyQt5 Logic Replication
- [x] Same AI prompts and processing stages
- [x] Same AWS services list validation
- [x] Same service mapping logic
- [x] Same adjacency list creation
- [x] Same node positioning approach
- [x] Same edge generation logic

## 🐛 Troubleshooting

### Common Issues

1. **Azure OpenAI Not Configured**
   - Ensure all environment variables are set
   - Check API key and endpoint validity
   - Verify deployment name and API version

2. **Image Upload Fails**
   - Check file size (max 20MB)
   - Verify file type (PNG/JPG/JPEG only)
   - Ensure proper CORS configuration

3. **Processing Timeout**
   - Increase `AZURE_OPENAI_TIMEOUT_SECONDS`
   - Check Azure OpenAI service availability
   - Verify image complexity (simpler images process faster)

4. **Invalid Architecture Data**
   - Check AI response format
   - Verify service name mapping
   - Ensure proper connection format

## 🔮 Future Enhancements

1. **Batch Processing**
   - Support multiple image uploads
   - Parallel processing capabilities

2. **Advanced AI Features**
   - Custom prompt templates
   - Architecture validation
   - Automatic optimization suggestions

3. **Enhanced UI/UX**
   - Real-time processing preview
   - Interactive editing during processing
   - Advanced error recovery

4. **Integration Features**
   - Direct integration with cloud providers
   - Template generation from processed images
   - Architecture comparison tools

## 📝 Notes

- The implementation maintains 100% compatibility with existing web application architecture
- All PyQt5 MapleGUI prompts and logic are preserved exactly
- The feature integrates seamlessly with existing save/load functionality
- Processing times depend on image complexity and Azure OpenAI response times
- The system supports the same AWS services catalog as the original PyQt5 application
