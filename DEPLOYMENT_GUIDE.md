# Load Image Functionality - Deployment Guide

This guide provides step-by-step instructions for deploying and testing the Load Image functionality in your environment.

## 🚀 Quick Start

### 1. Backend Setup

```bash
# Navigate to backend directory
cd backend

# Activate virtual environment
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install/update dependencies
pip install openai==1.12.0 Pillow==10.2.0

# Set environment variables
export AZURE_OPENAI_VISION_API_KEY="your_api_key_here"
export AZURE_OPENAI_VISION_ENDPOINT="https://your-endpoint.openai.azure.com/"
export AZURE_OPENAI_VISION_DEPLOYMENT_NAME="gpt-4o"
export AZURE_OPENAI_VISION_API_VERSION="2024-02-15-preview"

# Run tests
python test_load_image.py

# Start server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 2. Frontend Setup

```bash
# Navigate to frontend directory
cd frontend

# Install dependencies (if needed)
npm install

# Start development server
npm run dev
```

### 3. Test the Implementation

```bash
# Test API endpoints (in another terminal)
cd backend
python test_api_endpoints.py

# Access frontend test page
# Navigate to: http://localhost:3000/test/load-image
```

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the backend directory:

```env
# Azure OpenAI Vision Configuration
AZURE_OPENAI_VISION_API_KEY=your_api_key_here
AZURE_OPENAI_VISION_ENDPOINT=https://your-endpoint.openai.azure.com/
AZURE_OPENAI_VISION_DEPLOYMENT_NAME=gpt-4o
AZURE_OPENAI_VISION_API_VERSION=2024-02-15-preview
AZURE_OPENAI_TIMEOUT_SECONDS=60

# Image Upload Settings
MAX_IMAGE_SIZE=20971520
ALLOWED_IMAGE_TYPES=.png,.jpg,.jpeg
IMAGE_UPLOAD_DIRECTORY=uploads/images

# Database and other existing settings...
```

### Azure OpenAI Setup

1. **Create Azure OpenAI Resource**:
   - Go to Azure Portal
   - Create a new Azure OpenAI resource
   - Deploy GPT-4o model with vision capabilities

2. **Get Credentials**:
   - Copy API key from Azure portal
   - Copy endpoint URL
   - Note the deployment name

3. **Test Connection**:
   ```bash
   python -c "
   from app.services.image_processing_service import ImageProcessingService
   service = ImageProcessingService()
   print('Azure OpenAI client:', 'Connected' if service.azure_client else 'Not connected')
   "
   ```

## 🧪 Testing

### Backend Tests

```bash
cd backend

# Run comprehensive service tests
python test_load_image.py

# Expected output:
# ✅ Found 67 AWS services
# ✅ Azure OpenAI client initialized successfully
# ✅ All Image Processing Service tests completed!

# Test API endpoints (requires server running)
python test_api_endpoints.py

# Expected output:
# ✅ FastAPI server is running
# ✅ Health check passed
# ✅ Endpoint requires authentication (expected)
```

### Frontend Tests

1. **Component Test Page**:
   ```
   http://localhost:3000/test/load-image
   ```

2. **Architecture Designer**:
   ```
   http://localhost:3000/architecture
   ```
   - Click "Load from Image" button
   - Upload a test image
   - Verify processing and architecture generation

### Manual Testing Workflow

1. **Create Test Image**:
   - Draw a simple architecture diagram
   - Include AWS services (Lambda, S3, DynamoDB, etc.)
   - Add arrows showing connections
   - Save as PNG/JPG

2. **Upload and Process**:
   - Open Architecture Designer
   - Click "Load from Image"
   - Upload your test image
   - Wait for AI processing

3. **Verify Results**:
   - Check identified services
   - Verify connections
   - Test editing capabilities
   - Save/load architecture

## 🐛 Troubleshooting

### Common Issues

1. **Azure OpenAI Not Connected**:
   ```bash
   # Check credentials
   echo $AZURE_OPENAI_VISION_API_KEY
   echo $AZURE_OPENAI_VISION_ENDPOINT
   
   # Test connection
   python test_load_image.py
   ```

2. **File Upload Fails**:
   ```bash
   # Check file size and type
   ls -la your_image.png
   file your_image.png
   
   # Ensure file is under 20MB and is PNG/JPG
   ```

3. **Processing Timeout**:
   ```bash
   # Increase timeout in config
   export AZURE_OPENAI_TIMEOUT_SECONDS=120
   ```

4. **Authentication Errors**:
   ```bash
   # Check JWT token in browser localStorage
   # Ensure user is logged in
   ```

### Debug Mode

Enable debug logging:

```python
# In backend/app/main.py
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Health Check

```bash
curl http://localhost:8000/api/v1/image-processing/health
```

Expected response:
```json
{
  "message": "Image processing service status: healthy",
  "data": {
    "overall_status": "healthy",
    "azure_openai_vision": "healthy",
    "supported_image_types": [".png", ".jpg", ".jpeg"],
    "aws_services_count": 67
  }
}
```

## 📊 Performance

### Expected Processing Times

- **Small images** (< 1MB): 10-30 seconds
- **Medium images** (1-5MB): 30-60 seconds
- **Large images** (5-20MB): 60-120 seconds

### Optimization Tips

1. **Image Size**: Resize large images to reduce processing time
2. **Complexity**: Simpler diagrams process faster
3. **Caching**: Consider implementing result caching for repeated uploads

## 🔒 Security

### File Upload Security

- File type validation (PNG/JPG/JPEG only)
- File size limits (20MB max)
- Content type verification
- Temporary file cleanup

### API Security

- JWT authentication required
- Rate limiting recommended
- Input validation and sanitization

## 📈 Monitoring

### Key Metrics

- Image processing success rate
- Average processing time
- Azure OpenAI API usage
- Error rates by error type

### Logging

Monitor these log entries:
- Image upload events
- Processing start/completion
- Azure OpenAI API calls
- Error conditions

## 🚀 Production Deployment

### Environment Setup

1. **Production Environment Variables**:
   ```bash
   ENVIRONMENT=production
   AZURE_OPENAI_VISION_API_KEY=prod_key
   AZURE_OPENAI_VISION_ENDPOINT=prod_endpoint
   ```

2. **Resource Scaling**:
   - Consider Azure OpenAI rate limits
   - Implement request queuing for high load
   - Monitor memory usage for large images

3. **Backup and Recovery**:
   - Backup uploaded images (optional)
   - Log processing results
   - Implement retry mechanisms

### Load Testing

```bash
# Test with multiple concurrent uploads
# Monitor Azure OpenAI quota usage
# Verify error handling under load
```

## 📞 Support

### Getting Help

1. **Check Logs**: Review backend logs for detailed error messages
2. **Test Components**: Use the test scripts to isolate issues
3. **Verify Configuration**: Ensure all environment variables are set
4. **Azure OpenAI Status**: Check Azure service health

### Common Solutions

- **Restart Services**: Sometimes a simple restart resolves issues
- **Clear Cache**: Clear browser cache and localStorage
- **Update Dependencies**: Ensure all packages are up to date
- **Check Permissions**: Verify file system permissions for uploads

The Load Image functionality is now ready for production use! 🎉
