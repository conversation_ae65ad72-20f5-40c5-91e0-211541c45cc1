#!/usr/bin/env python3
"""
Test script for Load Image functionality

This script tests the image processing service and API endpoints
to ensure the PyQt5 MapleGUI Load Image functionality is properly
replicated in the web application.
"""

import asyncio
import base64
import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from app.services.image_processing_service import ImageProcessingService
from app.core.config import settings


async def test_image_processing_service():
    """Test the ImageProcessingService functionality"""
    print("🧪 Testing Image Processing Service")
    print("=" * 50)
    
    # Initialize service
    service = ImageProcessingService()
    
    # Test 1: Check AWS services list
    print("\n1. Testing AWS Services List")
    aws_services = service.get_aws_services_catalog()
    print(f"   ✓ Found {len(aws_services)} AWS services")
    print(f"   ✓ Sample services: {aws_services[:5]}")
    
    # Test 2: Check Azure OpenAI client initialization
    print("\n2. Testing Azure OpenAI Client")
    if service.azure_client:
        print("   ✓ Azure OpenAI client initialized successfully")
    else:
        print("   ⚠️  Azure OpenAI client not initialized (check credentials)")
    
    # Test 3: Test service mapping
    print("\n3. Testing Service Mapping")
    test_services = ["AWS Lambda", "Amazon S3", "Amazon DynamoDB", "User"]
    for service_name in test_services:
        mapped_service = service._map_service_to_web_format(service_name)
        print(f"   ✓ {service_name} -> {mapped_service['name']}")
    
    # Test 4: Test adjacency list creation
    print("\n4. Testing Adjacency List Creation")
    test_edges = [
        ("User", "AWS Lambda"),
        ("AWS Lambda", "Amazon DynamoDB"),
        ("Amazon DynamoDB", "Amazon S3"),
        ("Amazon S3", "User")
    ]
    adjacency_list = service._create_adjacency_list(test_edges)
    print(f"   ✓ Created adjacency list with {len(adjacency_list)} nodes")
    for node, connections in adjacency_list.items():
        print(f"     {node} -> {connections}")
    
    # Test 5: Test node generation
    print("\n5. Testing Node Generation")
    test_service_counts = ["User", "AWS Lambda", "Amazon DynamoDB", "Amazon S3"]
    nodes = service._generate_nodes(test_service_counts, adjacency_list)
    print(f"   ✓ Generated {len(nodes)} nodes")
    for node in nodes:
        print(f"     {node['id']}: {node['data']['label']}")
    
    # Test 6: Test edge generation
    print("\n6. Testing Edge Generation")
    edges = service._generate_edges(test_edges)
    print(f"   ✓ Generated {len(edges)} edges")
    for edge in edges:
        print(f"     {edge['source']} -> {edge['target']}")
    
    print("\n✅ All Image Processing Service tests completed!")


def test_configuration():
    """Test configuration settings"""
    print("\n🔧 Testing Configuration")
    print("=" * 50)
    
    # Check image upload settings
    print(f"Max image size: {settings.MAX_IMAGE_SIZE / (1024*1024):.1f}MB")
    print(f"Allowed image types: {settings.ALLOWED_IMAGE_TYPES}")
    print(f"Image upload directory: {settings.IMAGE_UPLOAD_DIRECTORY}")
    
    # Check Azure OpenAI settings
    print(f"Azure OpenAI Vision API Key: {'✓ Set' if settings.AZURE_OPENAI_VISION_API_KEY else '❌ Not set'}")
    print(f"Azure OpenAI Vision Endpoint: {'✓ Set' if settings.AZURE_OPENAI_VISION_ENDPOINT else '❌ Not set'}")
    print(f"Azure OpenAI Vision Deployment: {settings.AZURE_OPENAI_VISION_DEPLOYMENT_NAME}")
    print(f"Azure OpenAI Vision API Version: {settings.AZURE_OPENAI_VISION_API_VERSION}")
    print(f"Azure OpenAI Timeout: {settings.AZURE_OPENAI_TIMEOUT_SECONDS}s")


def create_test_image():
    """Create a simple test image for testing"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # Create a simple architecture diagram
        img = Image.new('RGB', (800, 600), color='white')
        draw = ImageDraw.Draw(img)
        
        # Draw simple boxes representing services
        # User box
        draw.rectangle([50, 50, 150, 100], outline='blue', width=2)
        draw.text((75, 70), "User", fill='blue')
        
        # Lambda box
        draw.rectangle([300, 50, 450, 100], outline='orange', width=2)
        draw.text((325, 70), "AWS Lambda", fill='orange')
        
        # DynamoDB box
        draw.rectangle([300, 200, 450, 250], outline='green', width=2)
        draw.text((315, 220), "DynamoDB", fill='green')
        
        # S3 box
        draw.rectangle([550, 200, 650, 250], outline='red', width=2)
        draw.text((575, 220), "S3", fill='red')
        
        # Draw arrows
        # User -> Lambda
        draw.line([(150, 75), (300, 75)], fill='black', width=2)
        draw.polygon([(290, 70), (300, 75), (290, 80)], fill='black')
        
        # Lambda -> DynamoDB
        draw.line([(375, 100), (375, 200)], fill='black', width=2)
        draw.polygon([(370, 190), (375, 200), (380, 190)], fill='black')
        
        # DynamoDB -> S3
        draw.line([(450, 225), (550, 225)], fill='black', width=2)
        draw.polygon([(540, 220), (550, 225), (540, 230)], fill='black')
        
        # S3 -> User (return path)
        draw.line([(600, 200), (600, 150), (100, 150), (100, 100)], fill='black', width=2)
        draw.polygon([(95, 110), (100, 100), (105, 110)], fill='black')
        
        # Save test image
        test_image_path = backend_dir / "test_architecture.png"
        img.save(test_image_path)
        print(f"✓ Created test image: {test_image_path}")
        return test_image_path
        
    except ImportError:
        print("⚠️  Pillow not installed, cannot create test image")
        return None


async def test_image_processing_with_mock():
    """Test image processing with a mock image"""
    print("\n🖼️  Testing Image Processing with Mock Data")
    print("=" * 50)
    
    service = ImageProcessingService()
    
    # Create mock image data
    test_image_path = create_test_image()
    if not test_image_path or not test_image_path.exists():
        print("❌ Cannot test without image file")
        return
    
    # Test image validation
    print("\n1. Testing Image Validation")
    with open(test_image_path, 'rb') as f:
        file_content = f.read()
    
    # Mock UploadFile object
    class MockUploadFile:
        def __init__(self, filename, content, content_type):
            self.filename = filename
            self.content = content
            self.content_type = content_type
            self.size = len(content)
            self._position = 0

        async def read(self):
            return self.content

        def seek(self, position):
            self._position = position

        def tell(self):
            return self._position
    
    mock_file = MockUploadFile("test_architecture.png", file_content, "image/png")
    
    try:
        await service._validate_image_file(mock_file)
        print("   ✓ Image validation passed")
    except Exception as e:
        print(f"   ❌ Image validation failed: {e}")
        return
    
    # Test base64 encoding
    print("\n2. Testing Base64 Encoding")
    try:
        encoded_image = await service._encode_image_to_base64(mock_file)
        print(f"   ✓ Image encoded to base64 ({len(encoded_image)} characters)")
    except Exception as e:
        print(f"   ❌ Base64 encoding failed: {e}")
        return
    
    # Test architecture data generation (without AI calls)
    print("\n3. Testing Architecture Data Generation")
    mock_service_counts = ["User", "AWS Lambda", "Amazon DynamoDB", "Amazon S3"]
    mock_connections = [
        "User-AWS Lambda",
        "AWS Lambda-Amazon DynamoDB", 
        "Amazon DynamoDB-Amazon S3",
        "Amazon S3-User"
    ]
    
    try:
        architecture_data = service._generate_architecture_data(mock_service_counts, mock_connections)
        print(f"   ✓ Generated architecture with {len(architecture_data['nodes'])} nodes and {len(architecture_data['edges'])} edges")
        
        # Print sample node
        if architecture_data['nodes']:
            sample_node = architecture_data['nodes'][0]
            print(f"   ✓ Sample node: {sample_node['id']} ({sample_node['data']['label']})")
        
        # Print sample edge
        if architecture_data['edges']:
            sample_edge = architecture_data['edges'][0]
            print(f"   ✓ Sample edge: {sample_edge['source']} -> {sample_edge['target']}")
            
    except Exception as e:
        print(f"   ❌ Architecture data generation failed: {e}")
        return
    
    print("\n✅ Mock image processing test completed!")


async def main():
    """Main test function"""
    print("🚀 Load Image Functionality Test Suite")
    print("=" * 60)
    
    # Test configuration
    test_configuration()
    
    # Test image processing service
    await test_image_processing_service()
    
    # Test with mock image
    await test_image_processing_with_mock()
    
    print("\n🎉 All tests completed!")
    print("\nNext steps:")
    print("1. Configure Azure OpenAI credentials in environment variables")
    print("2. Start the FastAPI backend server")
    print("3. Test the API endpoints with a real image")
    print("4. Test the frontend Load Image dialog")


if __name__ == "__main__":
    asyncio.run(main())
